import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  MenuItem,
  Typography,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { useMultiAccount } from "../../../helpers/context/MultiAccountContext";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";

const MultiAccountDropdown = ({ onSelect, onAddAccount }) => {
  const {
    selectedAccount,
    storedAccounts,
    handleAccountSelect,
    removeStoredAccount,
    loadingAccounts,
    switchingAccount,
  } = useMultiAccount();

  const [isVisible, setIsVisible] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const observer = useRef(null);
  const dropdownRef = useRef(null);

  // Get current login user ID
  const currentUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const loginUserId = currentUserData?.userId;

  // Initialize Intersection Observer when component mounts
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          // Disconnect once visible
          observer.current.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (dropdownRef.current) {
      observer.current.observe(dropdownRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  const handleAccountClick = async (account) => {
    await handleAccountSelect(account);
    onSelect?.(account);
  };

  const handleDeleteClick = (event, account) => {
    event.stopPropagation();
    setAccountToDelete(account);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (accountToDelete) {
      removeStoredAccount(accountToDelete.userId);
    }
    setDeleteDialogOpen(false);
    setAccountToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setAccountToDelete(null);
  };

  const isMainAccount = (account) => {
    return account.isMainAccount === true;
  };

  // If no accounts are loaded yet, show loading
  if (storedAccounts.length === 0 && !loadingAccounts) {
    return (
      <ul className="p-2 max-h-[300px] overflow-y-auto w-full sm:min-w-[300px] max-w-[100%]">
        <li className="flex items-center justify-center p-4">
          <CircularProgress size={24} />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Loading accounts...
          </Typography>
        </li>
      </ul>
    );
  }

  return (
    <>
      <ul
        ref={dropdownRef}
        className="p-2 max-h-[300px] overflow-y-auto w-full sm:min-w-[300px] max-w-[100%]"
      >
        {loadingAccounts ? (
          <li className="flex items-center justify-center p-4">
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Loading accounts...
            </Typography>
          </li>
        ) : storedAccounts.length > 0 ? (
          storedAccounts.map((account, index) => (
            <li
              key={account.userId || index}
              className={`bg-white border-gray-200 p-3 flex items-start sm:items-center gap-4 flex-wrap border hover:shadow-md transition cursor-pointer w-full
                ${
                  selectedAccount?.userId === account.userId
                    ? "border-[#563D39]"
                    : "border-gray-200"
                }
                ${index === 0 ? "rounded-t-lg" : ""}
                ${index === storedAccounts.length - 1 ? "rounded-b-lg" : ""}`}
              onClick={() => handleAccountClick(account)}
              disabled={switchingAccount}
              style={{
                opacity: switchingAccount ? 0.7 : 1,
                pointerEvents: switchingAccount ? "none" : "auto",
                cursor: switchingAccount ? "not-allowed" : "pointer",
              }}
            >
              <div className="relative">
                <img
                  src={
                    account.profileImage ||
                    siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                  }
                  alt="Profile"
                  className="h-10 w-10 min-w-[2.5rem] rounded-full border object-cover"
                  onError={(e) => {
                    e.target.src = siteConstant.SOCIAL_ICONS.DUMMY_PROFILE;
                  }}
                />
                {selectedAccount?.userId === account.userId && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-[#563D39] rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
                {switchingAccount &&
                  selectedAccount?.userId === account.userId && (
                    <div className="absolute inset-0 bg-white bg-opacity-75 rounded-full flex items-center justify-center">
                      <CircularProgress size={16} />
                    </div>
                  )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3 className="text-base font-medium text-gray-900 truncate">
                    {account.name}
                  </h3>
                  {isMainAccount(account) && (
                    <span className="px-2 py-1 text-xs bg-[#563D39] text-white rounded-full">
                      Main
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500 mb-1 truncate">
                  @{account.username}
                </p>
                <p className="text-xs text-gray-400 truncate">
                  {account.email}
                </p>
              </div>

              {!isMainAccount(account) && (
                <IconButton
                  size="small"
                  onClick={(e) => handleDeleteClick(e, account)}
                  sx={{
                    color: "#ef4444",
                    "&:hover": {
                      backgroundColor: "#fef2f2",
                    },
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </li>
          ))
        ) : (
          <li className="flex items-center justify-center p-4 text-gray-500">
            <Typography variant="body2">No accounts found</Typography>
          </li>
        )}

        {/* Add Account Button */}
        <li className="mt-2">
          <button
            onClick={onAddAccount}
            className="w-full p-3 flex items-center gap-3 text-[#563D39] hover:bg-gray-50 rounded-lg border border-dashed border-gray-300 transition-colors"
          >
            <AddIcon fontSize="small" />
            <span className="text-sm font-medium">Add Other Account</span>
          </button>
        </li>
      </ul>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Remove Account</DialogTitle>
        <DialogContent>
          <Typography>
            This will remove the account from this User. You'll have to sign in
            again to access it later.
            <br />
            Would you like to continue?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MultiAccountDropdown;
